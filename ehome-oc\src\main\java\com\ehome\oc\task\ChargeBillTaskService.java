package com.ehome.oc.task;

import com.ehome.common.core.domain.entity.SysUser;
import com.ehome.oc.service.charge.ChargeBillService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 收费账单定时任务服务
 */
@Service
public class ChargeBillTaskService {

    private static final Logger logger = LoggerFactory.getLogger(ChargeBillTaskService.class);

    @Autowired
    private ChargeBillService chargeBillService;

    /**
     * 自动生成账单定时任务
     * 每天凌晨1点执行
     */
    public void autoGenerateBills() {
        logger.info("开始执行自动生成账单定时任务");
        
        try {
            // 获取启用的收费绑定（不再依赖next_bill_time）
            List<Record> bindings = Db.find(
                    "select cb.*, cs.name as charge_standard_name " +
                    "from eh_charge_binding cb " +
                    "left join eh_charge_standard cs on cb.charge_standard_id = cs.id " +
                    "where cb.is_active = 1 and cs.is_active = 1 " +
                    "order by cb.community_id, cb.id");

            if (bindings.isEmpty()) {
                logger.info("没有需要生成账单的收费绑定");
                return;
            }

            logger.info("找到{}个需要生成账单的收费绑定", bindings.size());

            int successCount = 0;
            int failCount = 0;
            
            // 创建系统用户对象用于生成账单
            SysUser systemUser = createSystemUser();

            for (Record binding : bindings) {
                try {
                    Long billId = chargeBillService.generateBillByBinding(binding.getLong("id"), systemUser);
                    if (billId != null) {
                        successCount++;
                        logger.debug("成功生成账单，绑定ID：{}，账单ID：{}", binding.getLong("id"), billId);
                    }
                } catch (Exception e) {
                    failCount++;
                    logger.error("生成账单失败，绑定ID：{}，收费标准：{}，错误：{}", 
                            binding.getLong("id"), binding.getStr("charge_standard_name"), e.getMessage());
                }
            }

            logger.info("自动生成账单定时任务完成，成功：{}，失败：{}", successCount, failCount);
            
            // 记录任务执行结果
            recordTaskResult("AUTO_GENERATE_BILLS", successCount, failCount);
            
        } catch (Exception e) {
            logger.error("自动生成账单定时任务执行失败", e);
        }
    }


    /**
     * 检查并生成逾期违约金
     * 每天凌晨2点执行
     */
    public void generateLateFees() {
        logger.info("开始执行生成逾期违约金定时任务");
        
        try {
            long currentTime = System.currentTimeMillis() / 1000;
            
            // 获取逾期未缴费的账单
            List<Record> overdueBills = Db.find(
                    "select cb.*, css.late_money_type, css.penalty_rule " +
                    "from eh_charge_bill cb " +
                    "left join eh_charge_standard_snapshot css on cb.ci_snapshot_id = css.id " +
                    "where cb.pay_status = 0 and cb.end_time < ? and css.late_money_type = 1 " +
                    "and cb.late_money_amount = 0",
                    currentTime);

            if (overdueBills.isEmpty()) {
                logger.info("没有需要生成违约金的逾期账单");
                return;
            }

            logger.info("找到{}个需要生成违约金的逾期账单", overdueBills.size());

            int processCount = 0;
            for (Record bill : overdueBills) {
                try {
                    // 计算违约金
                    long lateFee = calculateLateFee(bill, currentTime);
                    if (lateFee > 0) {
                        // 更新账单违约金
                        Db.update("update eh_charge_bill set late_money_amount = ?, last_op_time = now() where id = ?",
                                lateFee, bill.getLong("id"));
                        processCount++;
                        logger.debug("更新账单违约金，账单ID：{}，违约金：{}分", bill.getLong("id"), lateFee);
                    }
                } catch (Exception e) {
                    logger.error("计算账单违约金失败，账单ID：{}，错误：{}", bill.getLong("id"), e.getMessage());
                }
            }

            logger.info("生成逾期违约金定时任务完成，处理{}个账单", processCount);
            
        } catch (Exception e) {
            logger.error("生成逾期违约金定时任务执行失败", e);
        }
    }

    /**
     * 创建系统用户对象
     */
    private SysUser createSystemUser() {
        SysUser systemUser = new SysUser();
        systemUser.setLoginName("SYSTEM");
        systemUser.setUserName("系统");
        systemUser.setCommunityId(""); // 将在使用时设置
        return systemUser;
    }

    /**
     * 计算违约金
     */
    private long calculateLateFee(Record bill, long currentTime) {
        // 简单实现：按天计算违约金
        long endTime = bill.getLong("end_time");
        long overdueDays = (currentTime - endTime) / (24 * 60 * 60);
        
        if (overdueDays <= 0) {
            return 0;
        }
        
        // 违约金 = 账单金额 * 0.05% * 逾期天数
        long billAmount = bill.getLong("bill_amount");
        long lateFee = (long) (billAmount * 0.0005 * overdueDays);
        
        return lateFee;
    }

    /**
     * 记录任务执行结果
     */
    private void recordTaskResult(String taskName, int successCount, int failCount) {
        try {
            Record taskLog = new Record();
            taskLog.set("task_name", taskName);
            taskLog.set("success_count", successCount);
            taskLog.set("fail_count", failCount);
            taskLog.set("execute_time", System.currentTimeMillis() / 1000);
            taskLog.set("create_time", System.currentTimeMillis() / 1000);
            
            // 这里可以保存到任务日志表，暂时只记录日志
            logger.info("任务执行结果 - 任务：{}，成功：{}，失败：{}", taskName, successCount, failCount);
        } catch (Exception e) {
            logger.error("记录任务执行结果失败", e);
        }
    }
}
